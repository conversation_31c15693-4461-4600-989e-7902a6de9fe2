resource "aws_security_group" "msk_sg" {
  name        = "msk_sg"
  description = "Allow traffic into msk cluster"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 9092
    to_port     = 9092
    protocol    = "tcp"
    cidr_blocks = ["**********/16"]
    description = "allows traffic from et prod vpc"
  }

  ingress {
    from_port   = 9092
    to_port     = 9092
    protocol    = "tcp"
    cidr_blocks = ["*********/16"]
    description = "allows traffic from shared services prod"
  }

  ingress {
    from_port   = 9094
    to_port     = 9094
    protocol    = "tcp"
    cidr_blocks = ["**********/16"]
    description = "allows traffic from et prod vpc"
  }

  ingress {
    from_port   = 9094
    to_port     = 9094
    protocol    = "tcp"
    cidr_blocks = ["*********/16"]
    description = "allows traffic from shared services prod"
  }
  ingress {
    from_port   = 9092
    to_port     = 9092
    protocol    = "tcp"
    cidr_blocks = ["**********/16"]
    description = "allows traffic from thankview vpc"
  }

  ingress {
    from_port   = 9094 
    to_port     = 9094
    protocol    = "tcp"
    cidr_blocks = ["**********/16"]
    description = "allows traffic from thankview vpc"
  }

  ingress {
    from_port   = 9098
    to_port     = 9098
    protocol    = "tcp"
    cidr_blocks = ["**********/16"]
    description = "allows traffic from thankview vpc"
  }

  ingress {
    from_port   = 9098
    to_port     = 9098
    protocol    = "tcp"
    cidr_blocks = ["**********/16"]
    description = "allows traffic from et prod vpc"
  }

  ingress {
    from_port   = 9098
    to_port     = 9098
    protocol    = "tcp"
    cidr_blocks = ["*********/16"]
    description = "allows traffic from shared services prod"
  }


  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name = "msk_sg"
  })
}
