data "aws_vpc" "shared_services_prod" {
  filter {
    name   = "tag:Name"
    values = ["prod"]
  }
}



data "aws_subnet" "AZ1" {
  filter {
    name   = "tag:Name"
    values = ["prod-private-us-east-1a"]
  }
}

data "aws_subnet" "AZ2" {
  filter {
    name   = "tag:Name"
    values = ["prod-private-us-east-1b"]
  }
}

data "aws_subnet" "AZ3" {
  filter {
    name   = "tag:Name"
    values = ["prod-private-us-east-1c"]
  }
}

data "aws_subnet" "AZ4" {
  filter {
    name   = "tag:Name"
    values = ["prod-private-us-east-1d"]
  }
}




data "aws_iam_role" "ec2_instance_role" {
  name     = "ec2_instance_role"  # Name of the EC2 instance role in Account A
}