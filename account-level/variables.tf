################################################################################
# Global variables
################################################################################

variable "commonTagsGlobal" {
  type    = map(string)
  default = {}
}


#########shared-infra-stage-subnets########

variable "shared_services_prod_subnets" {
  description = "List of subnet IDs for the shared-services-prod environment"
  type        = list(string)
  default     = [
    "subnet-00f8828afd7325edc",  // Private Subnet (AZ4)
    "subnet-0e9fbd55517652555",  // Public Subnet (AZ1)
    "subnet-0d5f5a5868cd072a6",  // Private Subnet (AZ2)
    "subnet-0f518d8ab2f09e5cc",  // Public Subnet (AZ6)
  ]
}

variable "tags" {
  type = map(string)
  default = {
    Terraform   = "true"
    Environment = "prod"
    Creator     = "<EMAIL>"
  }
}

variable "vpc_id" {
  type = string
  default = "vpc-098ea414abae1fb0d"
}
