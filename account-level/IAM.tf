
# MSK role
resource "aws_iam_role" "msk_access_role" {
  name     = "msk_access_role"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = [
            "arn:aws:iam::************:role/webserver-prod",
            "arn:aws:iam::************:role/video-worker-role-prod",
            "arn:aws:iam::************:role/sends-server-role-prod"
          ]
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# IAM Policy for MSK Access in Account B
resource "aws_iam_policy" "msk_access_policy" {
  name     = "msk_access_policy"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "kafka:DescribeCluster",
          "kafka:GetBootstrapBrokers",
          "kafka:DescribeConfiguration",
          "kafka:ListConfigurationRevisions",
          "kafka:DescribeClusterOperation",
          "kafka:ListClusters",
          "kafka:CreateTopic",
          "kafka:DescribeTopic",
          "kafka:DeleteTopic",
          "kafka:ListTopics"
        ]
        Resource = "*"
      }
    ]
  })
}

# Attach the policy to the role
resource "aws_iam_role_policy_attachment" "attach_msk_access_policy" {
  role       = aws_iam_role.msk_access_role.name
  policy_arn = aws_iam_policy.msk_access_policy.arn
}

