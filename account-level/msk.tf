resource "aws_msk_cluster" "prod_msk_cluster" {
  cluster_name           = "prod-msk-cluster"
  kafka_version          = "3.6.0"
  number_of_broker_nodes = 3

  broker_node_group_info {
    client_subnets  = [data.aws_subnet.AZ1.id, data.aws_subnet.AZ2.id, data.aws_subnet.AZ3.id]
    instance_type   = "kafka.m5.large"
    storage_info {
      ebs_storage_info {
        volume_size = 200
      }
    }
    security_groups = [aws_security_group.msk_sg.id] #update sec group
    
    connectivity_info {
      public_access {
        type = "DISABLED"
      }
    }
  }

  encryption_info {
    encryption_in_transit {
      client_broker = "TLS"
      in_cluster    = true
    }
  }

  client_authentication {
    sasl {
      iam   = true
      scram = false
    }
  }

  open_monitoring {
    prometheus {
      jmx_exporter {
        enabled_in_broker = true
      }
      node_exporter {
        enabled_in_broker = true
      }
    }
  }

  logging_info {
    broker_logs {
      cloudwatch_logs {
        enabled   = true
        log_group = aws_cloudwatch_log_group.msk_logs.name
      }
      s3 {
        enabled = true
        bucket  = "et-prod-aws-msk-kafka-cluster-logs"
        prefix  = "logs"
      }
    }
  }

  configuration_info {
    arn      = aws_msk_configuration.msk_config.arn
    revision = aws_msk_configuration.msk_config.latest_revision
  }

  tags = var.tags
}

resource "aws_msk_configuration" "msk_config" {
  name        = "prod-msk-cluster-configuration"
  description = "prod-msk-cluster configuration"
  kafka_versions = ["3.5.1"]
  server_properties = <<-PROPERTIES
auto.create.topics.enable=true
delete.topic.enable=true
PROPERTIES
}

resource "aws_cloudwatch_log_group" "msk_logs" {
  name = "MSKClusterLogs-prod-msk-cluster"
  tags = var.tags
}


#=================