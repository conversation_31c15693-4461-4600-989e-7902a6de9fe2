locals {
  environments = {
    prod = {
      cidr             = "10.82.0.0/16"
      azs              = ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d"]
      public_subnets   = ["10.82.0.0/20", "10.82.16.0/20", "10.82.32.0/20", "10.82.48.0/20"]
      private_subnets  = ["10.82.64.0/20", "10.82.80.0/20", "10.82.96.0/20", "10.82.112.0/20"]
      database_subnets = ["10.82.128.0/20", "10.82.144.0/20", "10.82.160.0/20", "10.82.176.0/20"]
      intra_subnets    = ["***********/20", "***********/20", "***********/20", "***********/20"]

      enable_nat_gateway                 = true
      single_nat_gateway                 = true
      one_nat_gateway_per_az             = false
      enable_vpn_gateway                 = false
      propagate_public_route_tables_vgw  = false
      propagate_private_route_tables_vgw = false
      propagate_intra_route_tables_vgw   = false
      create_database_subnet_group       = false
      enable_dns_hostnames               = true
      enable_dns_support                 = true
      enable_dhcp_options                = true
      dhcp_options_domain_name           = "shared-services-prod.evertrue.com"
      dhcp_options_domain_name_servers   = ["AmazonProvidedDNS"]
      tags                               = "${merge(var.commonTagsGlobal, { Creator = "<EMAIL>", Tier = "Prod", Component = "VPC", CreateDate = "20240515" })}"
      vpc_tags                           = { Name = "prod" }
      dhcp_options_tags                  = { Name = "prod-r53-DNS" }

      # public
      public_subnet_tags      = { Component = "public-subnet" }
      public_route_table_tags = { Name = "prod-public-route-table" }

      # private
      private_subnet_tags      = { Component = "private-subnet" }
      private_route_table_tags = { Name = "prod-private-route-table" }

      # database
      database_subnet_tags      = { Component = "database-subnet" }
      database_route_table_tags = { Name = "prod-database-route-table" }

      # intra
      intra_subnet_tags      = { Component = "internal-subnet" }
      intra_route_table_tags = { Name = "prod-internal-route-table" }
    }
  }
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "4.0.1"

  for_each = local.environments

  name = each.key

  cidr                  = try(each.value.cidr, "")
  secondary_cidr_blocks = try(each.value.secondary_cidr_blocks, [])

  azs              = try(each.value.azs, [])
  public_subnets   = try(each.value.public_subnets, [])
  private_subnets  = try(each.value.private_subnets, [])
  database_subnets = try(each.value.database_subnets, [])
  intra_subnets    = try(each.value.intra_subnets, [])

  create_database_subnet_group = try(each.value.create_database_subnet_group, false)

  enable_nat_gateway     = try(each.value.enable_nat_gateway, false)
  single_nat_gateway     = try(each.value.single_nat_gateway, false)
  one_nat_gateway_per_az = try(each.value.one_nat_gateway_per_az, false)

  enable_vpn_gateway                 = try(each.value.enable_vpn_gateway, false)
  propagate_public_route_tables_vgw  = try(each.value.propagate_public_route_tables_vgw, false)
  propagate_private_route_tables_vgw = try(each.value.propagate_private_route_tables_vgw, false)
  propagate_intra_route_tables_vgw   = try(each.value.propagate_intra_route_tables_vgw, false)


  enable_dns_hostnames             = try(each.value.enable_dns_hostnames, false)
  enable_dns_support               = try(each.value.enable_dns_support, false)
  enable_dhcp_options              = try(each.value.enable_dhcp_options, false)
  dhcp_options_domain_name         = try(each.value.dhcp_options_domain_name, "")
  dhcp_options_domain_name_servers = try(each.value.dhcp_options_domain_name_servers, [])

  tags              = try(each.value.tags, {})
  vpc_tags          = try(each.value.vpc_tags, {})
  dhcp_options_tags = try(each.value.dhcp_options_tags, {})

  # public
  public_subnet_suffix    = "public"
  public_subnet_tags      = try(each.value.public_subnet_tags, {})
  public_route_table_tags = try(each.value.public_route_table_tags, {})

  # private
  private_subnet_suffix    = "private"
  private_subnet_tags      = try(each.value.private_subnet_tags, {})
  private_route_table_tags = try(each.value.private_route_table_tags, {})

  # database
  database_subnet_suffix    = "database"
  database_subnet_tags      = try(each.value.database_subnet_tags, {})
  database_route_table_tags = try(each.value.database_route_table_tags, {})

  # intra
  intra_subnet_suffix    = "internal"
  intra_subnet_tags      = try(each.value.intra_subnet_tags, {})
  intra_route_table_tags = try(each.value.intra_route_table_tags, {})
}

# Our CIDR block is only big enough for public/private subnets so we need to "hand create" the aws_db_subnet_group with the id's of the private subnets
resource "aws_db_subnet_group" "database" {
  for_each = local.environments

  name        = lower(each.key)
  description = "Database subnet group for ${each.key}"
  subnet_ids  = module.vpc["prod"].private_subnets

  tags = merge(
    {
      "Name" = lower(each.key)
    },
    try(each.value.tags, {}),
    try(each.value.database_subnet_group_tags, {}),
  )
}